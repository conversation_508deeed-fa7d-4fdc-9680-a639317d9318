<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>A<PERSON> <PERSON></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .task-form {
            padding: 30px;
            border-bottom: 1px solid #eee;
        }

        .form-group {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .task-input {
            flex: 1;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .task-input:focus {
            outline: none;
            border-color: #667eea;
        }

        .add-btn {
            padding: 15px 25px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: transform 0.2s;
        }

        .add-btn:hover {
            transform: translateY(-2px);
        }

        .priority-select {
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            background: white;
        }

        .tasks-container {
            padding: 30px;
        }

        .filter-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            justify-content: center;
        }

        .filter-btn {
            padding: 10px 20px;
            border: 2px solid #667eea;
            background: white;
            color: #667eea;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .filter-btn.active,
        .filter-btn:hover {
            background: #667eea;
            color: white;
        }

        .task-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #667eea;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .task-item:hover {
            transform: translateX(5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .task-item.completed {
            opacity: 0.7;
            border-left-color: #28a745;
        }

        .task-item.completed .task-text {
            text-decoration: line-through;
        }

        .task-checkbox {
            width: 20px;
            height: 20px;
            cursor: pointer;
        }

        .task-content {
            flex: 1;
        }

        .task-text {
            font-size: 16px;
            margin-bottom: 5px;
        }

        .task-meta {
            font-size: 12px;
            color: #666;
            display: flex;
            gap: 15px;
        }

        .priority {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
        }

        .priority.alta {
            background: #dc3545;
            color: white;
        }

        .priority.media {
            background: #ffc107;
            color: #333;
        }

        .priority.baixa {
            background: #28a745;
            color: white;
        }

        .task-actions {
            display: flex;
            gap: 10px;
        }

        .edit-btn,
        .delete-btn {
            padding: 8px 12px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s;
        }

        .edit-btn {
            background: #17a2b8;
            color: white;
        }

        .delete-btn {
            background: #dc3545;
            color: white;
        }

        .edit-btn:hover,
        .delete-btn:hover {
            transform: scale(1.05);
        }

        .empty-state {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .empty-state img {
            width: 100px;
            opacity: 0.5;
            margin-bottom: 20px;
        }

        .stats {
            display: flex;
            justify-content: space-around;
            padding: 20px;
            background: #f8f9fa;
            margin-bottom: 20px;
            border-radius: 10px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
        }

        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }

        @media (max-width: 768px) {
            .form-group {
                flex-direction: column;
            }

            .filter-buttons {
                flex-wrap: wrap;
            }

            .task-item {
                flex-direction: column;
                align-items: flex-start;
            }

            .task-actions {
                align-self: flex-end;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📝 Meu App de Tarefas</h1>
            <p>Organize suas tarefas de forma simples e eficiente</p>
        </div>

        <div class="task-form">
            <div class="form-group">
                <input type="text" id="taskInput" class="task-input" placeholder="Digite sua nova tarefa...">
                <select id="prioritySelect" class="priority-select">
                    <option value="baixa">Baixa Prioridade</option>
                    <option value="media" selected>Média Prioridade</option>
                    <option value="alta">Alta Prioridade</option>
                </select>
                <button onclick="addTask()" class="add-btn">Adicionar</button>
            </div>
        </div>

        <div class="tasks-container">
            <div class="stats" id="stats">
                <div class="stat-item">
                    <div class="stat-number" id="totalTasks">0</div>
                    <div class="stat-label">Total</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="completedTasks">0</div>
                    <div class="stat-label">Concluídas</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="pendingTasks">0</div>
                    <div class="stat-label">Pendentes</div>
                </div>
            </div>

            <div class="filter-buttons">
                <button class="filter-btn active" onclick="filterTasks('all')">Todas</button>
                <button class="filter-btn" onclick="filterTasks('pending')">Pendentes</button>
                <button class="filter-btn" onclick="filterTasks('completed')">Concluídas</button>
                <button class="filter-btn" onclick="filterTasks('alta')">Alta Prioridade</button>
            </div>

            <div id="tasksList">
                <div class="empty-state">
                    <div style="font-size: 48px; margin-bottom: 20px;">📋</div>
                    <h3>Nenhuma tarefa ainda</h3>
                    <p>Adicione sua primeira tarefa acima!</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        let tasks = JSON.parse(localStorage.getItem('tasks')) || [];
        let currentFilter = 'all';
        let editingTaskId = null;

        function generateId() {
            return Date.now().toString(36) + Math.random().toString(36).substr(2);
        }

        function addTask() {
            const taskInput = document.getElementById('taskInput');
            const prioritySelect = document.getElementById('prioritySelect');
            const taskText = taskInput.value.trim();

            if (taskText === '') {
                alert('Por favor, digite uma tarefa!');
                return;
            }

            if (editingTaskId) {
                // Editando tarefa existente
                const taskIndex = tasks.findIndex(task => task.id === editingTaskId);
                if (taskIndex !== -1) {
                    tasks[taskIndex].text = taskText;
                    tasks[taskIndex].priority = prioritySelect.value;
                }
                editingTaskId = null;
                document.querySelector('.add-btn').textContent = 'Adicionar';
            } else {
                // Adicionando nova tarefa
                const newTask = {
                    id: generateId(),
                    text: taskText,
                    priority: prioritySelect.value,
                    completed: false,
                    createdAt: new Date().toLocaleString('pt-BR')
                };
                tasks.push(newTask);
            }

            taskInput.value = '';
            prioritySelect.value = 'media';
            saveTasks();
            renderTasks();
            updateStats();
        }

        function deleteTask(id) {
            if (confirm('Tem certeza que deseja excluir esta tarefa?')) {
                tasks = tasks.filter(task => task.id !== id);
                saveTasks();
                renderTasks();
                updateStats();
            }
        }

        function editTask(id) {
            const task = tasks.find(task => task.id === id);
            if (task) {
                document.getElementById('taskInput').value = task.text;
                document.getElementById('prioritySelect').value = task.priority;
                editingTaskId = id;
                document.querySelector('.add-btn').textContent = 'Atualizar';
                document.getElementById('taskInput').focus();
            }
        }

        function toggleTask(id) {
            const taskIndex = tasks.findIndex(task => task.id === id);
            if (taskIndex !== -1) {
                tasks[taskIndex].completed = !tasks[taskIndex].completed;
                saveTasks();
                renderTasks();
                updateStats();
            }
        }

        function filterTasks(filter) {
            currentFilter = filter;
            
            // Atualizar botões ativos
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            renderTasks();
        }

        function getFilteredTasks() {
            switch (currentFilter) {
                case 'pending':
                    return tasks.filter(task => !task.completed);
                case 'completed':
                    return tasks.filter(task => task.completed);
                case 'alta':
                    return tasks.filter(task => task.priority === 'alta');
                default:
                    return tasks;
            }
        }

        function renderTasks() {
            const tasksList = document.getElementById('tasksList');
            const filteredTasks = getFilteredTasks();

            if (filteredTasks.length === 0) {
                tasksList.innerHTML = `
                    <div class="empty-state">
                        <div style="font-size: 48px; margin-bottom: 20px;">📋</div>
                        <h3>${currentFilter === 'all' ? 'Nenhuma tarefa ainda' : 'Nenhuma tarefa encontrada'}</h3>
                        <p>${currentFilter === 'all' ? 'Adicione sua primeira tarefa acima!' : 'Tente outro filtro.'}</p>
                    </div>
                `;
                return;
            }

            tasksList.innerHTML = filteredTasks.map(task => `
                <div class="task-item ${task.completed ? 'completed' : ''}">
                    <input type="checkbox" class="task-checkbox" ${task.completed ? 'checked' : ''} 
                           onchange="toggleTask('${task.id}')">
                    <div class="task-content">
                        <div class="task-text">${task.text}</div>
                        <div class="task-meta">
                            <span class="priority ${task.priority}">${task.priority.toUpperCase()}</span>
                            <span>Criada em: ${task.createdAt}</span>
                        </div>
                    </div>
                    <div class="task-actions">
                        <button class="edit-btn" onclick="editTask('${task.id}')">Editar</button>
                        <button class="delete-btn" onclick="deleteTask('${task.id}')">Excluir</button>
                    </div>
                </div>
            `).join('');
        }

        function updateStats() {
            const total = tasks.length;
            const completed = tasks.filter(task => task.completed).length;
            const pending = total - completed;

            document.getElementById('totalTasks').textContent = total;
            document.getElementById('completedTasks').textContent = completed;
            document.getElementById('pendingTasks').textContent = pending;
        }

        function saveTasks() {
            localStorage.setItem('tasks', JSON.stringify(tasks));
        }

        // Event listener para Enter no input
        document.getElementById('taskInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                addTask();
            }
        });

        // Inicializar app
        renderTasks();
        updateStats();
    </script>
</body>
</html>
